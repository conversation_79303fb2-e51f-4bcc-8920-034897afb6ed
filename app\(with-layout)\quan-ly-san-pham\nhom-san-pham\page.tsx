'use client';
import {
    useDeleteProductGroups,
    useSearchProductGroups,
} from '@/apis/product-group/product-group.api';
import {
    ResponseSearchProductGroup,
    SearchProductGroup,
} from '@/apis/product-group/product-group.type';
import ButtonHeader from '@/components/common/ButtonHeader';

import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import ModalDelete from '@/components/common/Modal/ModalDelete';

import { ROUTES } from '@/lib/routes';
import { MRT_RowSelectionState } from 'mantine-react-table';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    <PERSON><PERSON>,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Row,
} from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';
import { ACTIONS } from './_types/action.type';
import { showToastSuccess } from '@/utils/toast-message';
import { getOneMonthAgo, getToday } from '@/utils/time';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default:
                mod.default as typeof mod.default<ResponseSearchProductGroup>,
        })),
    {
        ssr: false,
    },
);
const transformData = (
    items: ResponseSearchProductGroup[],
): ResponseSearchProductGroup[] => {
    return items.map((item) => ({
        ...item,
        childrens: item.childrens ? transformData(item.childrens) : undefined,
    }));
};

const ProductGroups = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);
    const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>({});
    const methods = useForm<SearchProductGroup>({
        defaultValues: {
            Page: 1,
            PageSize: 10,
            IsDeleted: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const { control, setValue } = methods;
    const [
        Name,
        UserNameCreated,
        ParentId,
        Page,
        PageSize,
        IsDeleted,
        FromDate,
        ToDate,
    ] = useWatch({
        control,
        name: [
            'Name',
            'UserNameCreated',
            'ParentId',
            'Page',
            'PageSize',
            'IsDeleted',
            'FromDate',
            'ToDate',
        ],
    });

    const handleSelectedAction = (
        action: ACTIONS,
        row?: ResponseSearchProductGroup,
    ) => {
        if (!row) {
            return;
        }
        switch (action) {
            case ACTIONS.DELETE:
                setSelectedIds([row.id]);
                setSelectedNames([row.name]);
                setModal(true);

                break;
            case ACTIONS.EDIT:
                router.push(
                    ROUTES.PRODUCT_MANAGEMENT.PRODUCT_GROUPS.UPDATE.replace(
                        ':id',
                        row.id,
                    ),
                );
                break;
            case ACTIONS.VIEW_DETAIL:
                router.push(
                    ROUTES.PRODUCT_MANAGEMENT.PRODUCT_GROUPS.DETAIL.replace(
                        ':id',
                        row.id,
                    ),
                );
                break;
            default:
                console.error('Action not found');
                break;
        }
    };

    const columns = useGetColumn({
        onSelectedAction: handleSelectedAction,
        page: 'list',
    });

    const { data, refetch, isLoading } = useSearchProductGroups({
        Name,
        UserNameCreated,
        ParentId,
        Page,
        PageSize,
        IsDeleted,
        FromDate,
        ToDate,
    });
    const { items: listProductGroup = [], totalItems = 0 } = data ?? {};
    const { mutate: deleteProductGroups } = useDeleteProductGroups({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa nhóm sản phẩm thành công',
                message:
                    'Thông tin nhóm sản phẩm đã được xóa thành công trong hệ thống.',
            });
            setModal(false);
            setSelectedIds([]);
            setSelectedNames([]);
            refetch();
        },
        onError: (error) => {
            const status = error.status;
            if (status === 400 || 401) {
                toast.warning(error.message);
                setModal(false);
                return;
            }
            toast.error(error.message);
        },
    });

    const handleCreate = () => {
        router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCT_GROUPS.CREATE);
    };
    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };
    const handleConfimDelete = () => {
        deleteProductGroups({
            ids: selectedIds,
            isDeleted: false,
        });
    };

    const handleDelete = () => {
        setModal(true);
    };

    const handleRowSelectionChange = (
        updater:
            | MRT_RowSelectionState
            | ((old: MRT_RowSelectionState) => MRT_RowSelectionState),
    ) => {
        const newRowSelection =
            typeof updater === 'function' ? updater(rowSelection) : updater;
        setRowSelection(newRowSelection);
    };

    const getItemByPath = (
        tree: ResponseSearchProductGroup[],
        path: string,
    ): ResponseSearchProductGroup | null => {
        const indexes = path.split('.').map(Number);
        let current: ResponseSearchProductGroup[] = tree;
        let item = null;
        for (const idx of indexes) {
            if (!Array.isArray(current) || current.length <= idx) {
                return null;
            }
            item = current[idx];
            current = item.childrens || [];
        }
        return item;
    };
    useEffect(() => {
        const selectedRowIds = Object.keys(rowSelection).filter(
            (key) => rowSelection[key],
        );
        const selectedItems = selectedRowIds
            .map((path) => getItemByPath(listProductGroup, path))
            .filter(Boolean) as ResponseSearchProductGroup[];
        let allSelectedIds: string[] = [];
        let allSelectedNames: string[] = [];
        selectedItems.forEach((item) => {
            allSelectedIds.push(item.id);
            allSelectedNames.push(item.name);
        });
        allSelectedIds = [...new Set(allSelectedIds)];
        allSelectedNames = [...new Set(allSelectedNames)];

        setSelectedIds((prev) => {
            if (JSON.stringify(prev) !== JSON.stringify(allSelectedIds)) {
                return allSelectedIds;
            }
            return prev;
        });
        setSelectedNames((prev) => {
            if (JSON.stringify(prev) !== JSON.stringify(allSelectedNames)) {
                return allSelectedNames;
            }
            return prev;
        });
    }, [rowSelection, listProductGroup]);

    const dataForTable = transformData(listProductGroup);

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col md={12}>
                    <ButtonHeader
                        showExportButton={false}
                        onCreateNew={handleCreate}
                        showDateFilters={true}
                    />
                </Col>
                <Col md={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <div className='d-flex gap-3'>
                                        <InputSearchNameWithApiControl
                                            name='Name'
                                            placeholder='Tìm kiếm theo tên nhóm sản phẩm'
                                        />

                                        <ComboboxSelectUserControl
                                            name='UserNameCreated'
                                            placeholder='Người tạo'
                                            style={{ width: '250px' }}
                                        />
                                    </div>
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        {selectedIds.length > 0 && (
                                            <Button
                                                style={{
                                                    backgroundColor: 'red',
                                                    border: 'none',
                                                    color: 'white',
                                                }}
                                                onClick={() => handleDelete()}
                                            >
                                                Xóa
                                            </Button>
                                        )}
                                        <Button
                                            outline
                                            className='filter-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-filter-line text-primary'></i>
                                        </Button>
                                        <Dropdown
                                            isOpen={dropdownOpen}
                                            toggle={toggleDropdown}
                                            direction='down'
                                        >
                                            <DropdownToggle
                                                outline
                                                className='settings-button'
                                                style={{
                                                    border: 'none',
                                                    backgroundColor: '#dff0fa',
                                                }}
                                            >
                                                <i className='ri-settings-2-line text-info'></i>
                                            </DropdownToggle>
                                            <DropdownMenu>
                                                <DropdownItem
                                                    onClick={() =>
                                                        router.push(
                                                            ROUTES
                                                                .PRODUCT_MANAGEMENT
                                                                .PRODUCT_GROUPS
                                                                .RESTORE,
                                                        )
                                                    }
                                                >
                                                    Khôi phục tài khoản
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={dataForTable}
                            isLoading={isLoading}
                            totalItems={Number(totalItems)}
                            onPageChange={(page: number) => {
                                setValue('Page', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                enableExpanding: true,
                                enableExpandAll: false,
                                enableRowSelection: true,
                                enableMultiRowSelection: true,
                                state: {
                                    rowSelection,
                                },
                                onRowSelectionChange: handleRowSelectionChange,
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                getSubRows: (row) => row.childrens,
                                displayColumnDefOptions: {
                                    'mrt-row-expand': {
                                        size: 0,
                                        minSize: 0,
                                        maxSize: 0,
                                        enableResizing: false,
                                        header: '',
                                        Cell: () => null,
                                    },
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>

            <ModalDelete
                onDelete={handleConfimDelete}
                onClose={handleClose}
                isOpen={modal}
                page='nhóm sản phẩm'
                data={selectedNames}
            />
        </FormProvider>
    );
};

export default dynamic(() => Promise.resolve(ProductGroups), { ssr: false });
