import { IContract } from '@/apis/contracts/contracts.type';
import { ICustomer } from '@/apis/customer/customer.type';

const logoPreviewWord = `${window.location.origin}/assets/images/logoPreviewWord/logo.jpg`;

const formatVietnameseDate = (dateString: string): string => {
    if (!dateString) {
        return '___';
    }

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return '___';
        }

        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        return `ngày ${day} tháng ${month} năm ${year}`;
    } catch {
        return '___';
    }
};

const formatContractToHTML = (
    contract: IContract,
    dataCustomer?: ICustomer,
): string => {
    const html = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Hợp đồng kinh tế</title>
            <style>
                body {
                    font-family: 'Times New Roman', serif;
                    font-size: 13px;
                    line-height: 1.5;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                    color: #000;
                }

                /* Container chính cho mỗi trang A4 */
                .page-container {
                    width: 210mm;
                    min-height: 297mm;
                    background-color: #ffffff;
                    margin: 0 auto 20px auto;
                    padding: 20mm 20mm 25mm 20mm;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                    position: relative;
                    box-sizing: border-box;
                    overflow: visible;
                    page-break-after: always;
                }

                /* Header cố định cho mỗi trang */
                .page-header {
                    position: absolute;
                    top: 15mm;
                    left: 20mm;
                    right: 20mm;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .logo-container {
                    width: 80px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 1px solid #ddd;
                    background-color: #fafafa;
                }

                .logo-container img {
                    max-width: 70px;
                    max-height: 50px;
                    object-fit: contain;
                }

                /* Nội dung chính của trang */
                .page-content {
                    margin-top: 70px;
                    padding-bottom: 20px;
                    max-height: calc(297mm - 20mm - 25mm - 70px - 20px);
                    overflow: hidden;
                }

                /* Header văn bản cho trang đầu */
                .document-header {
                    text-align: center;
                    margin-bottom: 30px;
                }

                .document-title {
                    font-weight: bold;
                    font-size: 16px;
                    margin: 15px 0 5px 0;
                    text-transform: uppercase;
                }

                .document-subtitle {
                    font-style: italic;
                    font-size: 14px;
                    margin: 5px 0 20px 0;
                }

                .contract-title {
                    font-weight: bold;
                    font-size: 18px;
                    margin: 25px 0 10px 0;
                    text-transform: uppercase;
                }

                .contract-number {
                    font-style: italic;
                    font-size: 14px;
                    margin-bottom: 30px;
                }

                /* Styling cho nội dung */
                .content-section {
                    margin: 15px 0;
                    text-align: justify;
                    line-height: 1.6;
                }

                .legal-basis {
                    text-indent: 30px;
                    margin: 20px 0;
                }

                .party-section {
                    margin: 20px 0;
                }

                .party-title {
                    font-weight: bold;
                    font-size: 14px;
                    margin: 20px 0 10px 0;
                    text-transform: uppercase;
                }

                .party-info {
                    margin-left: 20px;
                    line-height: 1.8;
                }

                .info-row {
                    margin: 8px 0;
                    display: flex;
                    align-items: flex-start;
                }

                .info-label {
                    min-width: 120px;
                    font-weight: bold;
                    flex-shrink: 0;
                }

                .info-value {
                    flex: 1;
                }

                .highlight {
                    background-color: #ffff99;
                    padding: 2px 4px;
                    font-weight: bold;
                }

                /* Styling cho các điều khoản */
                .article-title {
                    font-weight: bold;
                    font-size: 14px;
                    text-decoration: underline;
                    margin: 25px 0 15px 0;
                    text-transform: uppercase;
                }

                .article-content {
                    margin: 10px 0;
                    text-align: justify;
                    line-height: 1.6;
                }

                .sub-article {
                    margin: 15px 0;
                    padding-left: 20px;
                }

                /* Lists */
                ul, ol {
                    margin: 15px 0;
                    padding-left: 40px;
                }

                li {
                    margin: 8px 0;
                    line-height: 1.6;
                }

                /* Tables */
                .product-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    font-size: 12px;
                }

                .product-table th,
                .product-table td {
                    border: 1px solid #000;
                    padding: 8px;
                    text-align: center;
                    vertical-align: middle;
                }

                .product-table th {
                    background-color: #f0f0f0;
                    font-weight: bold;
                }

                .product-table .text-left {
                    text-align: left;
                }

                .product-table .text-right {
                    text-align: right;
                }

                /* Signature section */
                .signature-section {
                    margin-top: 40px;
                    display: flex;
                    justify-content: space-between;
                }

                .signature-box {
                    width: 45%;
                    text-align: center;
                }

                .signature-title {
                    font-weight: bold;
                    font-size: 14px;
                    margin-bottom: 5px;
                }

                .signature-note {
                    font-style: italic;
                    font-size: 12px;
                    margin-bottom: 60px;
                }

                .signature-name {
                    font-weight: bold;
                    font-size: 14px;
                }

                /* Page number */
                .page-number {
                    position: absolute;
                    bottom: 10mm;
                    left: 50%;
                    transform: translateX(-50%);
                    font-style: italic;
                    color: #666;
                    font-size: 11px;
                }

                /* Print styles */
                @media print {
                    body {
                        background-color: white;
                        padding: 0;
                    }

                    .page-container {
                        box-shadow: none;
                        margin: 0;
                        page-break-after: always;
                        max-height: none;
                    }

                    .page-container:last-child {
                        page-break-after: auto;
                    }

                    @page {
                        size: A4;
                        margin: 0;
                    }
                }

                /* Responsive adjustments */
                @media screen and (max-width: 768px) {
                    .page-container {
                        width: 100%;
                        min-height: auto;
                        max-height: none;
                        padding: 20px;
                        margin: 10px;
                    }

                    .signature-section {
                        flex-direction: column;
                    }

                    .signature-box {
                        width: 100%;
                        margin: 20px 0;
                    }
                }
            </style>
        </head>
        <body>
            <!-- TRANG 1 -->
            <div class="page-container">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="${logoPreviewWord}" alt="Logo" />
                    </div>
                </div>

                <div class="page-content">
                    <div class="document-header">
                        <div class="document-title">CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</div>
                        <div class="document-subtitle">Độc lập – Tự do – Hạnh phúc</div>

                        <div class="contract-title">HỢP ĐỒNG KINH TẾ</div>
                        <div class="contract-number">Số: <span class="highlight">${contract.contractNumber}</span></div>
                    </div>

                    <div class="content-section legal-basis">
                        Căn cứ vào Bộ Luật dân sự số 91/2015/QH13 có hiệu lực từ ngày 01/01/2017;<br>
                        Căn cứ vào Bộ Luật thương mại số 36/2005/QH11 có hiệu lực từ ngày 01/01/2006;<br>
                        Căn cứ vào nhu cầu thực tế của các bên liên quan.
                    </div>

                    <div class="content-section">
                        Hôm nay, <strong>${formatVietnameseDate(contract.signDate) || ''}</strong> tại văn phòng làm việc, hai bên thống nhất ký kết hợp đồng với các điều khoản sau:
                    </div>

                    <div class="party-section">
                        <div class="party-title">BÊN MUA: <span class="highlight">${dataCustomer?.name || ''}</span></div>
                        <div class="party-info">
                            <div class="info-row">
                                <span class="info-label">Địa chỉ:</span>
                                <span class="info-value">KCN Tăng Hòa, Phường Tăng Nhơn Phú A, Tp Thủ Đức, Tp Hồ Chí Minh</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Điện thoại:</span>
                                <span class="info-value">_______________</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">MST:</span>
                                <span class="info-value">${dataCustomer?.taxCode || ''}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Đại diện là:</span>
                                <span class="info-value">
                                    <span class="highlight">${contract.buyer?.representative || ''}</span>
                                    &nbsp;&nbsp;&nbsp;&nbsp;Chức vụ: <span class="highlight">${contract.buyer?.position || ''}</span>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Gọi tắt là:</span>
                                <span class="info-value">Bên A</span>
                            </div>
                        </div>
                    </div>

                    <div class="party-section">
                        <div class="party-title">BÊN BÁN: <span class="highlight">CÔNG TY CỔ PHẦN TƯ VẤN VÀ DỊCH VỤ CÔNG NGHỆ ASIC</span></div>
                        <div class="party-info">
                            <div class="info-row">
                                <span class="info-label">Địa chỉ:</span>
                                <span class="info-value">Phòng 1701, Tòa CT1, Khu đô thị Việt Hưng, Giang Biên, Long Biên, Hà Nội</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Điện thoại:</span>
                                <span class="info-value">024.********</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">MST:</span>
                                <span class="info-value">**********</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Tài khoản:</span>
                                <span class="info-value">${contract.seller?.bankAccount || ''} ${contract.seller?.bankName || ''} ${contract.seller?.bankBranch || ''}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Đại diện là:</span>
                                <span class="info-value">
                                    <span class="highlight">${contract.seller?.staffName || ''}</span>
                                    &nbsp;&nbsp;&nbsp;&nbsp;Chức vụ: <span class="highlight">${contract.seller?.position || ''}</span>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label"></span>
                                <span class="info-value">(Theo Ủy quyền số 150325-02/UQ-2025 ngày 15 tháng 03 năm 2025)</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Gọi tắt là:</span>
                                <span class="info-value">Bên B</span>
                            </div>
                        </div>
                    </div>

                    <div class="article-title">ĐIỀU 1: ĐỐI TƯỢNG VÀ GIÁ TRỊ HỢP ĐỒNG</div>
                    <div class="article-content">
                        Bên Bán đồng ý cung cấp danh mục thiết bị và dịch vụ cho Bên Mua theo danh mục hàng hóa đính kèm tại phụ lục 01 của hợp đồng.
                    </div>

                    <div class="article-title">ĐIỀU 2: CHẤT LƯỢNG VÀ TIÊU CHUẨN KỸ THUẬT CỦA HÀNG HÓA</div>
                    <div class="article-content">
                        Chất lượng hàng hóa mới 100%, đóng gói theo tiêu chuẩn của nhà sản xuất kèm theo đầy đủ tài liệu của nhà sản xuất.
                    </div>
                </div>

                <div class="page-number">Trang 1/6</div>
            </div>

            <!-- TRANG 2 -->
            <div class="page-container">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="${logoPreviewWord}" alt="Logo" />
                    </div>
                </div>

                <div class="page-content">
                    <div class="article-title">ĐIỀU 3: HÌNH THỨC THANH TOÁN</div>
                    <div class="article-content">
                        <div class="sub-article">
                            <strong>3.1</strong> Tổng giá trị hợp đồng: ................... VNĐ (Đã bao gồm thuế VAT)<br>
                            <em>(Bằng chữ: ...............................)</em>
                        </div>
                        <div class="sub-article">
                            <strong>3.2</strong> Hình thức thanh toán: Bằng chuyển khoản.
                        </div>
                        <div class="sub-article">
                            <strong>3.3</strong> Thanh toán: ${contract.payments.length || 0} đợt:
                        </div>

                        ${contract.payments
                            .map((payment, index) => {
                                const dotNumber = index + 1;
                                const paymentType =
                                    String(payment.paymentDocumentType) === '0'
                                        ? 'tạm ứng'
                                        : 'thanh toán';
                                const timePhrase = payment.daysAfterSign
                                    ? `trong vòng ${payment.daysAfterSign} ngày sau khi ký hợp đồng`
                                    : '';
                                const percentValue = payment.valuePercent
                                    ? `${payment.valuePercent}%`
                                    : '';

                                return `
                        <div class="sub-article">
                            <strong>Đợt ${dotNumber}:</strong> Bên A ${paymentType} ${
                                dotNumber > 1 ? 'tiếp ' : ''
                            }cho Bên B ${percentValue} giá trị hợp đồng ${timePhrase} với số tiền thanh toán là: ………………… VNĐ <em>(Bằng chữ: …………………………../)</em>.
                        </div>

                        <div class="sub-article">
                            Hồ sơ đề nghị ${paymentType} gồm:<br>
                            + ${String(payment.paymentDocumentType) === '0' ? 'Giấy đề nghị tạm ứng của Bên B' : 'Giấy bảo lãnh tạm ứng của ngân hàng'}: 01 bản gốc
                        </div>

                        <div class="sub-article">
                            Bảo lãnh ${paymentType} có hiệu lực kể từ ngày Bên B nhận được tiền ${paymentType} đợt ${dotNumber} theo Hợp đồng đến ngày hai bên ký Biên bản bàn giao Hàng Hóa.
                        </div>`;
                            })
                            .join('')}
                    </div>


                </div>

                <div class="page-number">Trang 2/6</div>
            </div>

            <!-- TRANG 3 -->
            <div class="page-container">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="${logoPreviewWord}" alt="Logo" />
                    </div>
                </div>

                <div class="page-content">
                    <div class="article-title">ĐIỀU 4: CUNG CẤP HÀNG HÓA VÀ CÁC TÀI LIỆU KÈM THEO</div>
                    <div class="article-content">
                        <div class="sub-article">
                            <strong>4.1</strong> Thời gian giao hàng dự kiến: Trong vòng ${contract.delivery.deliveryWeek || ''} tuần kể từ ngày nhận được tiền tạm ứng của bên A.
                        </div>

                        <div class="sub-article">
                            <strong>4.2</strong> Địa chỉ giao hàng: ${contract.delivery.addressDetail}, ${contract.delivery.ward}, ${contract.delivery.district}, ${contract.delivery.city}, ${contract.delivery.country}
                        </div>

                        <div class="sub-article">
                            <strong>4.3</strong> Tài liệu đi kèm khi giao hàng gồm:
                            <ul>
                                <li>Hóa đơn GTGT: ${contract.delivery.documentQuantity} bản điện tử</li>
                                <li>Biên bản bàn giao hàng: ${contract.delivery.documentQuantity} bản gốc</li>
                            </ul>
                        </div>
                    </div>

                    <div class="article-title">ĐIỀU 5: BẢO HÀNH</div>
                    <div class="article-content">
                        <div class="sub-article">
                            <strong>5.1</strong> Bên B bảo hành trong vòng 01 năm với máy chính và 03 tháng với phụ kiện kể từ ngày hai bên ký Biên bản nghiệm thu hàng hóa.
                        </div>

                        <div class="sub-article">
                            <strong>5.2</strong> Nếu thiết bị bị hư hỏng trong thời gian bảo hành, Bên A phải thông báo cho Bên B. Bên B sẽ liên lạc lại trong vòng 24h để xem xét, xác định nguyên nhân.
                        </div>

                        <div class="sub-article">
                            <strong>5.3</strong> Những hư hỏng có nguyên nhân do bên A sử dụng không đúng hướng dẫn sẽ được sửa chữa với chi phí do bên A chi trả.
                        </div>
                    </div>


                </div>

                <div class="page-number">Trang 3/6</div>
            </div>

            <!-- TRANG 4 -->
            <div class="page-container">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="${logoPreviewWord}" alt="Logo" />
                    </div>
                </div>

                <div class="page-content">
                    <div class="article-title">ĐIỀU 6: ĐIỀU KHOẢN PHẠT</div>
                    <div class="article-content">
                        <div class="sub-article">
                            <strong>6.1</strong> Bên B giao hàng chậm chịu phạt 0,05% giá trị hợp đồng/ngày chậm giao hàng nhưng tối đa không quá 5% giá trị hợp đồng.
                        </div>

                        <div class="sub-article">
                            <strong>6.2</strong> Bên A thanh toán chậm chịu phạt 0,05% giá trị hợp đồng/ngày chậm thanh toán nhưng tối đa không quá 5% giá trị hợp đồng.
                        </div>

                        <div class="sub-article">
                            <strong>6.3</strong> Trừ khi do lỗi của Bên kia, nếu Bên nào đơn phương chấm dứt hợp đồng sau khi hợp đồng có hiệu lực thì phải bồi thường cho Bên còn lại số tiền phạt 15% giá trị hợp đồng.
                        </div>
                    </div>

                    <div class="article-title">ĐIỀU 7: BẤT KHẢ KHÁNG</div>
                    <div class="article-content">
                        <div class="sub-article">
                            <strong>7.1</strong> Sự kiện bất khả kháng là sự kiện xảy ra mang tính khách quan và nằm ngoài tầm kiểm soát của các bên như động đất, bão, lụt, lốc, sóng thần, lở đất; hỏa hoạn; chiến tranh.
                        </div>

                        <div class="sub-article">
                            <strong>7.2</strong> Việc một bên không hoàn thành nghĩa vụ do sự kiện bất khả kháng sẽ không phải là cơ sở để bên kia chấm dứt hợp đồng.
                        </div>
                    </div>

                    <div class="article-title">ĐIỀU 8: ĐIỀU KHOẢN CHUNG</div>
                    <div class="article-content">
                        <div class="sub-article">
                            <strong>8.1</strong> Hai bên đồng ý cam kết thực hiện đúng, đầy đủ nghĩa vụ của mình như quy định trong hợp đồng trên tinh thần hợp tác.
                        </div>

                        <div class="sub-article">
                            <strong>8.2</strong> Nếu có tranh chấp không giải quyết được bằng thương lượng thì tranh chấp sẽ được đưa ra Tòa án Nhân dân Thành phố Hồ Chí Minh giải quyết.
                        </div>
                    </div>
                </div>

                <div class="page-number">Trang 4/6</div>
            </div>

            <!-- TRANG 5 -->
            <div class="page-container">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="${logoPreviewWord}" alt="Logo" />
                    </div>
                </div>

                <div class="page-content">
                    <div class="article-content">
                        <div class="sub-article">
                            <strong>8.3</strong> Hợp đồng này có hiệu lực kể từ ngày hai bên ký kết, không hủy ngang và có hiệu lực kể từ khi hai bên hoàn tất trách nhiệm đối với nhau mà không có bất cứ sự tranh chấp nào - Hợp đồng xem như đã hoàn thành.
                        </div>

                        <div class="sub-article">
                            <strong>8.4</strong> Hợp đồng này được lập thành 04 bản có giá trị pháp lý như nhau, mỗi bên giữ 02 bản để thực hiện.
                        </div>

                        <div class="sub-article">
                            <strong>8.5</strong> Các điều khoản khác không quy định trong hợp đồng này sẽ được thực hiện theo quy định của pháp luật Việt Nam hiện hành.
                        </div>
                    </div>

                    <div class="article-title">ĐIỀU 9: CHỮ KÝ CÁC BÊN</div>
                    <div class="signature-section">
                        <div class="signature-box">
                            <div class="signature-title">BÊN MUA</div>
                            <div class="signature-note">(Ký tên, đóng dấu)</div>
                            <div class="signature-name">${contract.buyer?.representative || ''}</div>
                        </div>
                        <div class="signature-box">
                            <div class="signature-title">BÊN BÁN</div>
                            <div class="signature-note">(Ký tên, đóng dấu)</div>
                            <div class="signature-name">VƯƠNG NGỌC LINH</div>
                        </div>
                    </div>
                </div>

                <div class="page-number">Trang 5/6</div>
            </div>

            <!-- TRANG 6 - PHỤ LỤC -->
            <div class="page-container">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="${logoPreviewWord}" alt="Logo" />
                    </div>
                </div>

                <div class="page-content">
                    <div class="document-header">
                        <div class="document-title">PHỤ LỤC 01: DANH MỤC HÀNG HÓA</div>
                        <div class="document-subtitle">(Kèm theo hợp đồng số ${contract.contractNumber || ''} ký ${formatVietnameseDate(contract.signDate)})</div>
                    </div>

                    <div class="content-section">
                        <table class="product-table">
                            <thead>
                                <tr>
                                    <th style="width: 8%;">STT</th>
                                    <th style="width: 35%;">Mô tả hàng hóa</th>
                                    <th style="width: 20%;">Mã hiệu/Model</th>
                                    <th style="width: 10%;">Số lượng</th>
                                    <th style="width: 12%;">Đơn giá</th>
                                    <th style="width: 15%;">Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="height: 35px;">
                                    <td>1</td>
                                    <td class="text-left">_________________________</td>
                                    <td>_____________</td>
                                    <td>_____</td>
                                    <td class="text-right">___________</td>
                                    <td class="text-right">___________</td>
                                </tr>
                                <tr style="height: 35px;">
                                    <td>2</td>
                                    <td class="text-left">_________________________</td>
                                    <td>_____________</td>
                                    <td>_____</td>
                                    <td class="text-right">___________</td>
                                    <td class="text-right">___________</td>
                                </tr>
                                <tr style="height: 35px;">
                                    <td>3</td>
                                    <td class="text-left">_________________________</td>
                                    <td>_____________</td>
                                    <td>_____</td>
                                    <td class="text-right">___________</td>
                                    <td class="text-right">___________</td>
                                </tr>
                                <tr style="font-weight: bold; background-color: #f0f0f0;">
                                    <td colspan="5" style="text-align: center;">TỔNG CỘNG</td>
                                    <td class="text-right">___________</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="content-section" style="margin-top: 30px;">
                        <div style="text-align: center; font-style: italic;">
                            (Bằng chữ: ________________________________________________)
                        </div>
                    </div>

                    <div class="signature-section" style="margin-top: 40px;">
                        <div class="signature-box">
                            <div class="signature-title">BÊN MUA</div>
                            <div class="signature-note">(Ký tên, đóng dấu)</div>
                            <div class="signature-name">${contract.buyer?.representative || ''}</div>
                        </div>
                        <div class="signature-box">
                            <div class="signature-title">BÊN BÁN</div>
                            <div class="signature-note">(Ký tên, đóng dấu)</div>
                            <div class="signature-name">VƯƠNG NGỌC LINH</div>
                        </div>
                    </div>
                </div>

                <div class="page-number">Trang 6/6</div>
            </div>

        </body>
        </html>
    `;
    return html;
};

export const generateWordPreviewFromHTML = async (
    contract: IContract,
    dataCustomer?: ICustomer,
) => {
    try {
        const htmlContent = formatContractToHTML(contract, dataCustomer);
        const newWindow = window.open('', '_blank');
        if (newWindow) {
            // Sử dụng phương pháp hiện đại hơn thay vì document.write
            newWindow.document.open();
            newWindow.document.documentElement.innerHTML = htmlContent;
            newWindow.document.close();

            // Đảm bảo tài liệu được render hoàn toàn
            newWindow.addEventListener('load', () => {
                newWindow.focus();
            });
        }
    } catch (error) {
        console.error('Error generating Word preview:', error);
        throw new Error('Không thể tạo file xem trước');
    }
};

export const generateWordPreview = generateWordPreviewFromHTML;
